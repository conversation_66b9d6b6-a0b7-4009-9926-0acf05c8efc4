import cv2
import numpy as np
import os

def create_output_dirs():
    """创建输出目录"""
    # dirs = ['registered', 'denoised', 'segmented', 'detected']
    dirs = ['registered', 'denoised', 'segmented','post_processed']
    for dir_path in dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

def save_frame(frame, frame_index, folder_name, prefix=""):
    """保存帧到指定文件夹"""
    filename = f"{folder_name}/{prefix}frame_{frame_index:04d}.png"
    cv2.imwrite(filename, frame)
    return filename

def draw_bounding_boxes(frame, bboxes):
    """在帧上绘制边界框"""
    result = frame.copy()
    for bbox in bboxes:
        x, y, w, h = bbox
        cv2.rectangle(result, (x, y), (x + w, y + h), (0, 255, 0), 2)
    return result
