import cv2
import numpy as np
from scipy import ndimage

def otsu_threshold(image):
    """
    使用Otsu方法进行阈值分割

    参数:
        image: 输入图像

    返回:
        最优阈值和分割后的二值图像
    """
    # 确保图像是灰度图
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 使用OpenCV内置的Otsu方法计算最优阈值
    ret, binary_image = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    return ret, binary_image

def adaptive_threshold(image, block_size=11, C=2):
    """
    自适应阈值分割

    参数:
        image: 输入图像
        block_size: 局部区域大小，必须是奇数
        C: 常数，用于调整阈值

    返回:
        分割后的二值图像
    """
    # 确保图像是灰度图
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 确保block_size是奇数
    if block_size % 2 == 0:
        block_size += 1

    # 使用高斯滤波平滑图像
    smoothed = cv2.GaussianBlur(image, (5, 5), 0)

    # 应用自适应阈值分割
    binary = cv2.adaptiveThreshold(smoothed, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                  cv2.THRESH_BINARY, block_size, C)

    return binary

# def post_process(binary_image, morph_op='open', kernel_size=3):
#     """
#     对二值图像进行后处理，如开闭运算去除噪点或填充小孔

#     参数:
#         binary_image: 二值图像
#         morph_op: 形态学操作，可选 'open', 'close', 'dilate', 'erode'
#         kernel_size: 结构元素大小

#     返回:
#         处理后的二值图像
#     """
#     # 创建结构元素
#     kernel = np.ones((kernel_size, kernel_size), np.uint8)

#     # 应用形态学操作
#     if morph_op == 'open':
#         # 开运算（先腐蚀后膨胀），去除小的噪点
#         result = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel)
#     elif morph_op == 'close':
#         # 闭运算（先膨胀后腐蚀），填充小孔
#         result = cv2.morphologyEx(binary_image, cv2.MORPH_CLOSE, kernel)
#     elif morph_op == 'dilate':
#         # 膨胀操作
#         result = cv2.dilate(binary_image, kernel, iterations=1)
#     elif morph_op == 'erode':
#         # 腐蚀操作
#         result = cv2.erode(binary_image, kernel, iterations=1)
#     else:
#         # 默认不做处理
#         result = binary_image

#     return result

def segment_image(image, method='otsu', block_size=11, C=2,  kernel_size=3):
    """
    图像分割函数

    参数:
        image: 输入图像
        method: 分割方法，可选 'otsu' 或 'adaptive'
        block_size: 自适应分割的局部区域大小
        C: 自适应分割的常数
        post_process_op: 后处理操作，可选 'open', 'close', 'dilate', 'erode' 或 None
        kernel_size: 后处理的结构元素大小

    返回:
        分割后的二值图像
    """
    # 确保图像是灰度图
    if len(image.shape) == 3:
        gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray_image = image.copy()

    # 应用预处理 - 增强对比度
    # 使用CLAHE（对比度受限的自适应直方图均衡化）
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(gray_image)

    # 根据指定方法进行分割
    if method.lower() == 'otsu' or method.lower() == 'global':
        _, binary = otsu_threshold(enhanced)
    elif method.lower() == 'adaptive':
        binary = adaptive_threshold(enhanced, block_size, C)
    else:
        print(f"未知的分割方法: {method}，使用Otsu阈值分割")
        _, binary = otsu_threshold(enhanced)

    # # 应用后处理（如果指定）
    # if post_process_op:
    #     binary = post_process(binary, post_process_op, kernel_size)

    return binary
