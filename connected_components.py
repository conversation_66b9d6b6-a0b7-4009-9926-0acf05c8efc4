import cv2
import numpy as np
import matplotlib.pyplot as plt

def load_processed_image(image_path):
    """
    Loads a morphologically processed binary image.
    Ensures it's grayscale. For CCA, we'll typically want white objects on a black background.
    If the input image has black objects (0) on a white background (255),
    this function will invert it.
    """
    binary_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if binary_image is None:
        raise ValueError(f"无法加载图像: {image_path}")

    # Ensure the image is truly binary (0 and 255) for clarity
    # _, binary_image_thresh = cv2.threshold(binary_image, 127, 255, cv2.THRESH_BINARY)
    # The above line might alter the image if it's already correctly processed.
    # Let's assume the input 'morphologically processed image' is already mostly 0s and 255s.

    # cv2.connectedComponentsWithStats expects white objects (255) on a black background (0).
    # If your processed image has black shadows (0) on a white background (255), invert it.
    # We can check the dominant pixel value or assume based on previous steps.
    # Assuming shadows (objects of interest) were black (0) and background white (255).
    
    # A simple check: if white pixels are more numerous, black might be the object.
    # However, it's safer to assume the convention from previous steps.
    # If objects are black (0) on white (255) in the input `binary_image`:
    print("假设形态学处理后的图像中，目标区域为黑色(0)，背景为白色(255)。")
    print("将图像反转以进行连通区域分析 (目标变为白色，背景变为黑色)。")
    objects_as_white = cv2.bitwise_not(binary_image)
    
    # If your objects are already white (255) on black (0), you can skip bitwise_not:
    # objects_as_white = binary_image

    return binary_image, objects_as_white


def analyze_connected_components(image_with_white_objects):
    """
    Performs connected component analysis on a binary image where objects are white.

    Returns:
    num_labels, labels_matrix, stats, centroids
    """
    # connectivity = 8 # or 4
    # num_labels includes the background label (0)
    num_labels, labels_matrix, stats, centroids = cv2.connectedComponentsWithStats(
        image_with_white_objects, 
        connectivity=8, # Use 8-connectivity
        ltype=cv2.CV_32S
    )
    return num_labels, labels_matrix, stats, centroids


def visualize_cca_results(original_binary_image, num_labels, labels_matrix, stats, centroids):
    """
    Visualizes the results of Connected Component Analysis.
    - Shows the original binary image.
    - Shows an image with each component colored differently.
    - Shows the original image with bounding boxes and component numbers.
    """
    
    # --- 解决中文乱码的代码 ---
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    except Exception:
        try:
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
        except Exception:
            try:
                plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei']
                plt.rcParams['axes.unicode_minus'] = False
            except Exception:
                print("警告：未能自动设置中文字体，绘图中的中文可能显示为乱码。")
    # --- 中文乱码解决代码结束 ---

    # 1. Create a colored label image
    # Map component labels to hue val, 0-179 is hue range in OpenCV
    colored_labels = np.zeros((labels_matrix.shape[0], labels_matrix.shape[1], 3), dtype=np.uint8)
    # Background label is 0, so we start coloring from label 1
    for label in range(1, num_labels):
        # Create a random color for each component
        # More distinct colors can be generated if needed
        colored_labels[labels_matrix == label] = [np.random.randint(50, 256), 
                                                  np.random.randint(50, 256), 
                                                  np.random.randint(50, 256)]

    # 2. Create an image with bounding boxes and labels
    # Work on a BGR version of the original (if it's grayscale) or the inverted one
    # Let's draw on the original binary image where objects were black
    output_image_with_boxes = cv2.cvtColor(original_binary_image, cv2.COLOR_GRAY2BGR)

    for i in range(1, num_labels): # Skip background label 0
        left = stats[i, cv2.CC_STAT_LEFT]
        top = stats[i, cv2.CC_STAT_TOP]
        width = stats[i, cv2.CC_STAT_WIDTH]
        height = stats[i, cv2.CC_STAT_HEIGHT]
        area = stats[i, cv2.CC_STAT_AREA]
        cx, cy = centroids[i]

        # Draw bounding box
        cv2.rectangle(output_image_with_boxes, (left, top), (left + width, top + height), (0, 255, 0), 1) # Green box
        # Put component number (label)
        cv2.putText(output_image_with_boxes, str(i), (int(cx) - 5, int(cy) - 5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1) # Blue text

        # Optional: Filter small components by area
        # min_area = 50 # example minimum area
        # if area < min_area:
        #     continue # skip drawing for small components

    # # Display using matplotlib
    # plt.figure(figsize=(18, 6))

    # plt.subplot(1, 3, 1)
    # # Display original binary image (where objects are black)
    # plt.imshow(original_binary_image, cmap='gray', vmin=0, vmax=255)
    # plt.title('形态学处理后的图像 (黑白反转前)')
    # plt.axis('off')

    # plt.subplot(1, 3, 2)
    # plt.imshow(colored_labels) # Already BGR
    # plt.title(f'连通区域 (共 {num_labels-1} 个)')
    # plt.axis('off')

    # plt.subplot(1, 3, 3)
    # plt.imshow(cv2.cvtColor(output_image_with_boxes, cv2.COLOR_BGR2RGB)) # Matplotlib expects RGB
    # plt.title('带边界框和编号的连通区域')
    # plt.axis('off')
    
    # plt.tight_layout()  
    # plt.show()


# if __name__ == '__main__':
#     # 替换为您的形态学处理后的二值图像路径
#     # 这张图像应该是黑色的阴影 (0) 在白色的背景上 (255)
#     # processed_image_path = 'closed_after_opened_image.png' # Example if you saved from previous step
#     processed_image_path = 'post_processed\post_frame_0070.png' # Using the initially provided segmented image as input for CCA
#                                                # You should use the actual output of your morphological processing step.

#     try:
#         # 1. 加载经过形态学处理的图像 (例如，阴影为黑色0，背景为白色255)
#         #    并获取一个反转版本用于CCA (阴影为白色255，背景为黑色0)
#         original_morph_image, image_for_cca = load_processed_image(processed_image_path)
        
#         # 如果你的形态学处理输出已经是白前景黑背景，调整 load_processed_image 或直接传入
#         # e.g., if objects are already white: image_for_cca = cv2.imread(processed_image_path, cv2.IMREAD_GRAYSCALE)

#         # 2. 执行连通区域分析
#         num_labels, labels_matrix, stats, centroids = analyze_connected_components(image_for_cca)
        
#         print(f"找到的连通区域数量 (包括背景): {num_labels}")
#         print(f"实际目标区域数量: {num_labels - 1}")

#         # 打印一些统计信息 (可选)
#         # for i in range(1, num_labels): # Skip background
#         #     print(f"区域 {i}: 面积={stats[i, cv2.CC_STAT_AREA]}, 质心=({centroids[i][0]:.2f}, {centroids[i][1]:.2f})")

#         # 3. 可视化结果
#         visualize_cca_results(original_morph_image, num_labels, labels_matrix, stats, centroids)

#     except ValueError as e:
#         print(f"值错误: {e}")
#     except FileNotFoundError:
#         print(f"文件未找到: {processed_image_path}。请确保路径正确，并且文件存在。")
#     except Exception as e:
#         print(f"发生意外错误: {e}")