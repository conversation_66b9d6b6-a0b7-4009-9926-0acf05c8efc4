import cv2
import numpy as np
import matplotlib.pyplot as plt

# 添加以下代码来解决中文乱码问题
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei (黑体)
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号 '-' 显示为方块的问题

def preprocess_binary_image(image_input):
    """
    处理图像，确保它是灰度图，然后进行二值化。
    假设暗对象（阴影）在较亮的背景上。
    阈值处理使阴影变为0（黑色），背景变为255（白色）。

    参数:
        image_input: 可以是图像路径字符串或已加载的图像数组

    返回:
        二值化后的图像
    """
    # 检查输入类型
    if isinstance(image_input, str):
        # 如果是字符串，则作为文件路径读取图像
        gray_image = cv2.imread(image_input, cv2.IMREAD_GRAYSCALE)
        if gray_image is None:
            raise ValueError(f"无法加载图像: {image_input}")
    elif isinstance(image_input, np.ndarray):
        # 如果是数组，检查是否为灰度图
        if len(image_input.shape) == 3 and image_input.shape[2] == 3:
            # 彩色图像转灰度
            gray_image = cv2.cvtColor(image_input, cv2.COLOR_BGR2GRAY)
        else:
            # 已经是灰度图或二值图
            gray_image = image_input.copy()
    else:
        raise TypeError("输入必须是图像路径字符串或图像数组")

    # 对图像进行二值化处理
    # 使用一个通用阈值，例如127。如果图像已经是完美的0和255，此步骤影响不大但能确保格式。
    # cv2.THRESH_BINARY_INV 会使得低于阈值（暗区）的像素变为255，高于阈值的变为0。
    # 我们希望阴影为黑(0)，背景为白(255)，所以如果暗区是阴影，用THRESH_BINARY。
    # 从示例图像 seg_frame_0070.png 看，阴影已经是黑色了。
    # 如果您的分割结果已经是黑影(0)白底(255)，直接使用即可。
    # 如果是白影黑底，则需要反转或调整阈值类型。
    # 我们假设输入图像已经是黑影(0)白底(255)
    # _, binary_image = cv2.threshold(gray_image, 127, 255, cv2.THRESH_BINARY)
    # 如果图像已经是正确的二值图 (0代表黑色，255代表白色)，直接使用gray_image
    # 为确保一致性，可以做一个简单的检查和转换
    unique_values = np.unique(gray_image)
    if not (len(unique_values) <= 2 and (0 in unique_values or 255 in unique_values)):
        print("图像不是预期的二值格式 (0 和 255)，将重新阈值化...")
        _, binary_image = cv2.threshold(gray_image, np.median(gray_image), 255, cv2.THRESH_BINARY) # 暗区为0，亮区为255
        # 如果原始图像中阴影较亮，则用 cv2.THRESH_BINARY_INV
    else:
        binary_image = gray_image
        if 0 not in unique_values and 255 in unique_values: # 比如只有255和一些其他值
             _, binary_image = cv2.threshold(gray_image, np.min(gray_image[gray_image>0])-1 if len(gray_image[gray_image>0])>0 else 127, 255, cv2.THRESH_BINARY_INV) # 确保背景为白，目标为黑
        elif 255 not in unique_values and 0 in unique_values:
             _, binary_image = cv2.threshold(gray_image, 1, 255, cv2.THRESH_BINARY)


    # 确保图像确实是0和255
    binary_image[binary_image > 0] = 255


    return binary_image

def morphology_open_on_black_objects(binary_image, kernel_size=(3,3)):
    """
    对二值图像中的黑色对象（值为0）执行形态学开运算。
    此操作旨在去除小的黑色斑点/噪声。

    参数:
    binary_image (numpy.ndarray): 输入的二值图像 (黑色对象为0, 白色背景为255)。
    kernel_size (tuple): 形态学操作的核大小, 例如 (3,3) 或 (5,5)。

    返回:
    numpy.ndarray: 处理后的二值图像。
    """
    if not (0 in np.unique(binary_image) and 255 in np.unique(binary_image)) and len(np.unique(binary_image)) > 1 :
        print("警告: morph_open_on_black_objects 输入图像可能不是严格的0/255二值图。")

    # OpenCV的开运算 (MORPH_OPEN) 通常用于去除前景中的小对象。
    # 如果前景是白色 (255)，背景是黑色 (0)，则 MORPH_OPEN 会去除小白色斑点。
    # 由于我们的对象是黑色 (0)，背景是白色 (255)，我们需要：
    # 1. 反转图像 (黑色变白色，白色变黑色)。
    # 2. 对反转后的图像执行标准开运算 (去除小的白色斑点，这些斑点原先是小的黑色斑点)。
    # 3. 再次反转图像。

    img_inverted = cv2.bitwise_not(binary_image)

    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, kernel_size) # 或者 cv2.MORPH_ELLIPSE

    # 对反转后的图像（白色对象）执行开运算
    opened_inverted_image = cv2.morphologyEx(img_inverted, cv2.MORPH_OPEN, kernel)

    # 再次反转，得到结果
    result_image = cv2.bitwise_not(opened_inverted_image)

    return result_image

def morphology_close_on_black_objects(binary_image, kernel_size=(3,3)):
    """
    对二值图像中的黑色对象（值为0）执行形态学闭运算。
    此操作旨在填充黑色对象内部的小的白色孔洞。

    参数:
    binary_image (numpy.ndarray): 输入的二值图像 (黑色对象为0, 白色背景为255)。
    kernel_size (tuple): 形态学操作的核大小, 例如 (3,3) 或 (5,5)。

    返回:
    numpy.ndarray: 处理后的二值图像。
    """
    if not (0 in np.unique(binary_image) and 255 in np.unique(binary_image)) and len(np.unique(binary_image)) > 1 :
        print("警告: morph_close_on_black_objects 输入图像可能不是严格的0/255二值图。")

    # OpenCV的闭运算 (MORPH_CLOSE) 通常用于填充前景对象中的小孔洞。
    # 如果前景是白色 (255)，背景是黑色 (0)，则 MORPH_CLOSE 会填充白色对象中的小黑色孔洞。
    # 由于我们的对象是黑色 (0)，背景是白色 (255)，我们需要：
    # 1. 反转图像 (黑色变白色，白色变黑色)。现在，原来黑色对象内部的白色孔洞变成了白色对象内部的黑色孔洞。
    # 2. 对反转后的图像执行标准闭运算 (填充白色对象中的小黑色孔洞)。
    # 3. 再次反转图像。

    img_inverted = cv2.bitwise_not(binary_image)

    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, kernel_size) # 或者 cv2.MORPH_ELLIPSE

    # 对反转后的图像（白色对象）执行闭运算
    closed_inverted_image = cv2.morphologyEx(img_inverted, cv2.MORPH_CLOSE, kernel)

    # 再次反转，得到结果
    result_image = cv2.bitwise_not(closed_inverted_image)

    return result_image





def post_process(binary_image, kernel_size_open = (7, 7), kernel_size_close = (7, 7)):
    """
    对二值图像进行后处理，如开闭运算去除噪点或填充小孔

    参数:
        binary_image: 二值图像
        kernel_size_open: 开运算的结构元素大小
        kernel_size_close: 闭运算的结构元素大小

    返回:
        处理后的二值图像
    """
    # 1. 确保输入是图像数组而不是路径
    # 如果输入已经是图像数组，直接使用
    if isinstance(binary_image, np.ndarray):
        thresholded_image = binary_image.copy()
    else:
        # 如果输入是路径字符串，则使用preprocess_binary_image加载
        thresholded_image = preprocess_binary_image(binary_image)

    # 2. 形态学开运算：去除小的黑色噪点
    # 核的大小可以根据实际图像中噪点的大小调整

    opened_image = morphology_open_on_black_objects(thresholded_image, kernel_size_open)

    # 3. 形态学闭运算：填充黑色阴影内部的白色小孔洞
    # 核的大小可以根据实际图像中孔洞的大小调整
    # 通常在开运算之后进行闭运算，以处理清理过的对象

    closed_image_after_open = morphology_close_on_black_objects(opened_image, kernel_size_close)

    # 或者直接在原图上做闭运算 (可能效果不同)
    # closed_image_original = morphology_close_on_black_objects(thresholded_image, kernel_size_close)
    return closed_image_after_open


