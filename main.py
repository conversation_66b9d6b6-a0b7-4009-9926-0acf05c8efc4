import cv2
import numpy as np
import os
import argparse
from tqdm import tqdm
import torch
from registration import register_frames, visualize_registration
from denoising import adaptive_denoising
from segmentation import segment_image
from utils import create_output_dirs, save_frame, draw_bounding_boxes

from post_processing import post_process
from connected_components import analyze_connected_components
from detection import detect_motion_targets

def process_video(video_path, output_video_path=None,
                  filter_type='cattepm', seg_method='otsu',
                  iterations=30, # General iterations, primarily for CattePM
                  sigma_gaussian=2.0, K_diff=20.0, lambda_pm=0.125, # CattePM specific
                  lk_window_size=7, lee_damping=1.0, # Lee/Kuan specific
                  min_displacement=15.0, min_trajectory_length=3, max_distance=40.0,
                  min_area_similarity=0.2, min_area_threshold=30): # Motion detection specific
    """
    处理SAR视频，检测动目标

    参数:
        video_path: 输入视频路径
        output_video_path: 输出视频路径，如果为None则使用默认路径
        filter_type: 降噪滤波器类型，可选 'lee', 'kuan', 'cattepm'
        seg_method: 分割方法，可选 'otsu' 或 'adaptive'
        iterations: CattePM滤波器的迭代次数，默认为30
    """
    # 创建输出目录
    create_output_dirs()

    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频: {video_path}")
        return

    # 获取视频属性
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 设置输出视频
    if output_video_path is None:
        output_video_path = 'result_video.mp4'

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))

    # 读取第一帧
    ret, prev_frame = cap.read()
    if not ret:
        print("无法读取视频帧")
        return

    # 存储所有处理后的帧
    all_frames = [prev_frame]
    registered_frames = [prev_frame]  # 第一帧不需要配准

    # 对第一帧进行降噪处理
    denoised_frame = adaptive_denoising(prev_frame, filter_type=filter_type,
                                        sigma_gaussian=sigma_gaussian, K_diff=K_diff,
                                        lambda_val=lambda_pm, iterations=iterations,
                                        lee_kuan_window_size=lk_window_size,
                                        lee_damping_factor=lee_damping)
    denoised_frames = [denoised_frame]

    # 对降噪后的图像进行Otsu阈值分割
    segmented_frame = segment_image(denoised_frame, method=seg_method)
    segmented_frames = [segmented_frame]

    #对阈值分割后的图像进行后处理
    post_processed_frame = post_process(segmented_frame, kernel_size_open = (7, 7), kernel_size_close = (7, 7))
    post_processed_frames = [post_processed_frame]

    #对后处理后的图像进行连通区域分析



    # 保存第一帧结果
    save_frame(prev_frame, 0, 'registered', 'reg_')
    save_frame(denoised_frame, 0, 'denoised', 'den_')
    save_frame(segmented_frame, 0, 'segmented', 'seg_')
    save_frame(post_processed_frame, 0, 'post_processed', 'post_')



    # 处理剩余帧
    frame_idx = 1
    pbar = tqdm(total=frame_count, desc="处理视频帧")

    while True:
        ret, current_frame = cap.read()
        if not ret:
            break

        # 图像配准
        registered_frame, _ = register_frames(prev_frame, current_frame)

        # 保存配准结果
        save_frame(registered_frame, frame_idx, 'registered', 'reg_')

        # 降噪 - 使用指定滤波器
        denoised_frame = adaptive_denoising(registered_frame, filter_type=filter_type,
                                            sigma_gaussian=sigma_gaussian, K_diff=K_diff,
                                            lambda_val=lambda_pm, iterations=iterations,
                                            lee_kuan_window_size=lk_window_size,
                                            lee_damping_factor=lee_damping)
        save_frame(denoised_frame, frame_idx, 'denoised', 'den_')

        # 对降噪后的图像进行Otsu阈值分割
        segmented_frame = segment_image(denoised_frame, method=seg_method)
        save_frame(segmented_frame, frame_idx, 'segmented', 'seg_')


        # 后处理
        post_processed_frame = post_process(segmented_frame, kernel_size_open = (7, 7), kernel_size_close = (7, 7))
        save_frame(post_processed_frame, frame_idx, 'post_processed', 'post_')


        # 存储处理后的帧
        all_frames.append(current_frame)
        registered_frames.append(registered_frame)
        denoised_frames.append(denoised_frame)
        segmented_frames.append(segmented_frame)
        post_processed_frames.append(post_processed_frame)


        # 更新前一帧
        prev_frame = registered_frame.copy()

        frame_idx += 1
        pbar.update(1)

    pbar.close()
    cap.release()

    # 执行动目标检测
    print("\n开始动目标检测...")
    motion_trajectories = detect_motion_targets(
        post_processed_folder="post_processed",
        original_frames_folder="registered",
        output_folder="detected",
        min_displacement=min_displacement,
        min_trajectory_length=min_trajectory_length,
        max_distance=max_distance,
        min_area_similarity=min_area_similarity,
        min_area_threshold=min_area_threshold
    )

    print(f"\n动目标检测完成！检测到 {len(motion_trajectories)} 个运动目标")
    return motion_trajectories



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='SAR视频动目标检测')
    parser.add_argument('--video', type=str, default='3.mp4', help='输入SAR视频路径')
    parser.add_argument('--output', type=str, default='result_video.mp4', help='输出视频路径')

    # Denoising parameters
    parser.add_argument('--filter', type=str, default='cattepm',
                        choices=['lee', 'kuan', 'cattepm', 'bilateral', 'nlmeans'], # Added more choices
                        help='降噪滤波器类型')
    parser.add_argument('--iterations', type=int, default=10, help='CattePM滤波器的迭代次数 (或通用迭代参数)') # Adjusted default
    parser.add_argument('--sigma_gaussian', type=float, default=2.0, help='CattePM: 高斯平滑梯度的sigma')
    parser.add_argument('--K_diff', type=float, default=10.0, help='CattePM: 扩散系数的K值 (梯度阈值)')
    parser.add_argument('--lambda_pm', type=float, default=0.125, help='CattePM: 扩散速率 lambda')
    parser.add_argument('--lk_window_size', type=int, default=7, help='Lee/Kuan: 窗口大小 (奇数)')
    parser.add_argument('--lee_damping', type=float, default=1.0, help='Lee: 阻尼系数')

    # Segmentation parameters (as per previous state)
    parser.add_argument('--seg_method', type=str, default='otsu',
                        choices=['otsu', 'global', 'adaptive'],
                        help="分割方法: 'otsu' (或 'global') 使用Otsu全局阈值, 'adaptive' 使用自适应阈值。")
    # Add adaptive segmentation params if needed by segment_image function:
    # parser.add_argument('--adaptive_block_size', type=int, default=11, help='Adaptive seg: block size')
    # parser.add_argument('--adaptive_C', type=int, default=2, help='Adaptive seg: C value')

    # Motion detection parameters
    parser.add_argument('--min_displacement', type=float, default=50.0,
                        help='运动目标最小位移阈值（像素）')
    parser.add_argument('--min_trajectory_length', type=int, default=8,
                        help='运动目标最小轨迹长度（帧数）')
    parser.add_argument('--max_distance', type=float, default=30.0,
                        help='阴影区域匹配最大距离（像素）')
    parser.add_argument('--min_area_similarity', type=float, default=0.2,
                        help='阴影区域匹配最小面积相似度（0-1）')
    parser.add_argument('--min_area_threshold', type=int, default=50,
                        help='阴影区域最小面积阈值（像素²）')


    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f" Using device: {device}")

    args = parser.parse_args()

    # Ensure lk_window_size is odd
    if args.lk_window_size % 2 == 0:
        args.lk_window_size +=1
        print(f"Adjusted lk_window_size to be odd: {args.lk_window_size}")

    process_video(args.video, args.output,
                  filter_type=args.filter,
                  seg_method=args.seg_method,
                  iterations=args.iterations,
                  sigma_gaussian=args.sigma_gaussian,
                  K_diff=args.K_diff,
                  lambda_pm=args.lambda_pm,
                  lk_window_size=args.lk_window_size,
                  lee_damping=args.lee_damping,
                  min_displacement=args.min_displacement,
                  min_trajectory_length=args.min_trajectory_length,
                  max_distance=args.max_distance,
                  min_area_similarity=args.min_area_similarity,
                  min_area_threshold=args.min_area_threshold)
