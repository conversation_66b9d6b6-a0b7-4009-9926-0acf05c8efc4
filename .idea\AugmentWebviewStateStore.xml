<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>