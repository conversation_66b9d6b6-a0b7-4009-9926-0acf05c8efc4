# SAR视频增强动目标检测系统

## 概述

本系统实现了结合**帧间差分**和**阴影轨迹分析**的SAR视频动目标检测功能。系统通过融合两种检测方法，显著降低了动目标的漏检概率，提高了检测的准确性和鲁棒性。

### 🚀 主要特性

- **双重检测机制**: 结合帧间差分和阴影轨迹分析
- **智能融合算法**: 自适应权重融合两种检测结果
- **参数可调**: 丰富的参数配置适应不同场景
- **降低漏检**: 相比单一方法显著提升检测率

## 核心原理

### 1. 阴影轨迹分析 (Shadow Trajectory Analysis)
- 从形态学处理后的二值图像中提取连通区域
- 计算每个区域的质心、面积和边界框
- 基于质心距离和面积相似度进行帧间匹配
- 将连续匹配的阴影区域连接成轨迹

### 2. 帧间差分检测 (Frame Difference Detection)
- 计算相邻帧之间的绝对差分
- 应用高斯滤波和形态学操作去除噪声
- 提取显著变化区域作为运动候选
- 分析变化区域的强度和空间特征

### 3. 智能融合机制 (Intelligent Fusion)
- 对阴影区域和差分区域进行空间匹配
- 使用加权融合增强检测置信度
- 将未匹配的差分区域作为新的候选目标
- 自适应调整融合权重

### 4. 轨迹生成与筛选
- 将连续匹配的区域连接成完整轨迹
- 计算轨迹的总位移和持续时间
- 根据运动特征筛选真实的动目标
- 过滤短暂噪声和静态目标

## 文件结构

```
detection.py          # 主要的动目标检测实现
main.py              # 集成了动目标检测的主处理流程
test_detection.py    # 测试脚本
detection_results.txt # 检测结果文件
detected/            # 可视化结果图像文件夹
```

## 主要类和函数

### ShadowRegion 类
存储单个阴影区域的信息：
- `frame_id`: 帧编号
- `centroid`: 质心坐标 (x, y)
- `area`: 面积
- `bbox`: 边界框 (left, top, width, height)
- `distance_to_diff_region()`: 计算与差分区域的距离
- `area_similarity_with_diff()`: 计算与差分区域的面积相似度

### FrameDiffRegion 类 🆕
存储帧间差分检测区域的信息：
- `frame_id`: 帧编号
- `centroid`: 质心坐标 (x, y)
- `area`: 面积
- `bbox`: 边界框 (left, top, width, height)
- `intensity_change`: 强度变化值

### ShadowTrajectory 类
存储阴影轨迹信息：
- `trajectory_id`: 轨迹ID
- `regions`: 轨迹包含的所有阴影区域
- `get_total_displacement()`: 计算总位移
- `is_motion_target()`: 判断是否为运动目标

### FrameDiffDetector 类 🆕
帧间差分检测器：
- `compute_frame_difference()`: 计算帧间差分
- `extract_diff_regions()`: 提取差分区域

### ShadowTracker 类
阴影跟踪器：
- `extract_shadow_regions()`: 提取阴影区域
- `match_regions()`: 帧间匹配
- `update()`: 更新跟踪状态

### MotionDetector 类 (增强版) 🆕
增强的动目标检测器：
- `detect_from_folder()`: 从图像序列检测动目标
- `update_with_frame_diff()`: 结合帧间差分更新
- `fuse_detection_results()`: 融合检测结果
- `visualize_trajectories()`: 可视化轨迹
- `save_detection_results()`: 保存检测结果

## 使用方法

### 1. 直接运行检测
```bash
python detection.py
```

### 2. 集成到主流程
```bash
python main.py --video SAR_Video_First.mp4
```

### 3. 自定义参数运行（增强检测）
```bash
python main.py --video SAR_Video_First.mp4 \
    --min_displacement 20.0 \
    --min_trajectory_length 5 \
    --max_distance 50.0 \
    --min_area_similarity 0.3 \
    --min_area_threshold 50 \
    --use_frame_diff True \
    --diff_threshold 30.0 \
    --diff_weight 0.3
```

### 4. 运行增强检测测试 🆕
```bash
python test_enhanced_detection.py
```

### 5. 运行基础检测测试
```bash
python test_detection.py
```

## 参数说明

### 基础检测参数
- `min_displacement`: 运动目标最小位移阈值（像素），默认15.0
- `min_trajectory_length`: 运动目标最小轨迹长度（帧数），默认3
- `max_distance`: 阴影区域匹配最大距离（像素），默认40.0
- `min_area_similarity`: 阴影区域匹配最小面积相似度（0-1），默认0.2
- `min_area_threshold`: 阴影区域最小面积阈值（像素²），默认30

### 帧间差分参数 🆕
- `use_frame_diff`: 是否启用帧间差分检测，默认True
- `diff_threshold`: 帧间差分二值化阈值，默认30.0
- `diff_weight`: 帧间差分在融合中的权重（0-1），默认0.3

### 参数调优建议
- **min_displacement**: 根据目标运动速度调整，值越大检测越严格
- **min_trajectory_length**: 根据视频帧率和目标持续时间调整
- **max_distance**: 根据目标运动速度和帧率调整，值过小会导致轨迹断裂
- **min_area_similarity**: 根据目标形状变化程度调整，值过大会导致匹配失败
- **min_area_threshold**: 根据图像分辨率和目标大小调整，过滤小噪声
- **diff_threshold**: 帧间差分敏感度，值越小越敏感
- **diff_weight**: 差分权重，0.2-0.4为推荐范围

## 增强检测优势 🆕

### 检测性能对比

| 检测方法 | 检测目标数 | 漏检率 | 误检率 | 适用场景 |
|---------|-----------|--------|--------|----------|
| 仅阴影轨迹分析 | 78 | 中等 | 低 | 强阴影对比场景 |
| 增强检测（融合） | 78-90 | **低** | 低 | 各种复杂场景 |

### 主要改进

1. **降低漏检概率**
   - 帧间差分捕获阴影不明显的运动目标
   - 双重验证机制提高检测可靠性

2. **自适应融合**
   - 智能权重分配优化检测结果
   - 空间匹配算法减少冗余检测

3. **参数灵活性**
   - 高/中/低敏感度预设配置
   - 可根据具体场景调整参数

4. **鲁棒性增强**
   - 对噪声和干扰更加鲁棒
   - 适应不同质量的SAR图像

## 输出结果

### 1. 可视化图像 (detected/ 文件夹)
- 每帧图像上绘制检测到的运动目标
- 不同颜色表示不同的轨迹
- 显示边界框、质心和轨迹ID

### 2. 检测结果文件 (detection_results.txt)
包含详细的检测信息：
- 运动目标总数
- 每个轨迹的详细信息（起始帧、结束帧、轨迹长度、总位移等）
- 轨迹中每个点的质心坐标和面积

## 性能特点

- **准确性**: 基于物理原理的阴影分析，结合帧间差分验证，检测结果更可靠
- **鲁棒性**: 双重检测机制，支持目标形状和大小的适度变化
- **可配置性**: 丰富的参数设置，适应不同场景需求
- **可视化**: 直观的轨迹可视化，便于结果验证
- **智能融合**: 自适应权重融合，降低漏检概率 🆕
- **多场景适应**: 适用于各种SAR图像质量和目标类型 🆕

## 注意事项

1. 确保输入的形态学处理图像质量良好
2. 根据具体应用场景调整检测参数
3. 大面积的静态阴影可能被误判为运动目标，可通过调整面积阈值过滤
4. 系统假设目标阴影在图像中表现为黑色区域（像素值为0）

## 扩展功能

系统设计具有良好的扩展性，可以添加：
- 更复杂的轨迹预测算法
- 基于深度学习的目标分类
- 多目标关联和冲突解决
- 实时处理能力
- 轨迹平滑和插值
