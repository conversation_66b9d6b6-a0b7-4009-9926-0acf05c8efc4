# SAR视频动目标检测系统

## 概述

本系统实现了基于阴影轨迹分析的SAR视频动目标检测功能。系统通过分析形态学处理后的阴影区域，进行帧间匹配和轨迹跟踪，最终识别出具有运动特征的目标。

## 核心原理

### 1. 阴影区域提取
- 从形态学处理后的二值图像中提取连通区域
- 计算每个区域的质心、面积和边界框
- 过滤掉面积过小的噪声区域

### 2. 帧间匹配
- 基于质心距离和面积相似度进行阴影区域匹配
- 使用综合评分机制选择最佳匹配
- 支持可配置的匹配阈值参数

### 3. 轨迹生成
- 将连续匹配的阴影区域连接成轨迹
- 记录轨迹的起始帧、结束帧和所有中间点
- 计算轨迹的总位移和长度

### 4. 运动目标判定
- 根据总位移阈值判断是否为运动目标
- 根据轨迹长度过滤短暂的噪声轨迹
- 支持自定义判定参数

## 文件结构

```
detection.py          # 主要的动目标检测实现
main.py              # 集成了动目标检测的主处理流程
test_detection.py    # 测试脚本
detection_results.txt # 检测结果文件
detected/            # 可视化结果图像文件夹
```

## 主要类和函数

### ShadowRegion 类
存储单个阴影区域的信息：
- `frame_id`: 帧编号
- `centroid`: 质心坐标 (x, y)
- `area`: 面积
- `bbox`: 边界框 (left, top, width, height)

### ShadowTrajectory 类
存储阴影轨迹信息：
- `trajectory_id`: 轨迹ID
- `regions`: 轨迹包含的所有阴影区域
- `get_total_displacement()`: 计算总位移
- `is_motion_target()`: 判断是否为运动目标

### ShadowTracker 类
阴影跟踪器：
- `extract_shadow_regions()`: 提取阴影区域
- `match_regions()`: 帧间匹配
- `update()`: 更新跟踪状态

### MotionDetector 类
动目标检测器：
- `detect_from_folder()`: 从图像序列检测动目标
- `visualize_trajectories()`: 可视化轨迹
- `save_detection_results()`: 保存检测结果

## 使用方法

### 1. 直接运行检测
```bash
python detection.py
```

### 2. 集成到主流程
```bash
python main.py --video SAR_Video_First.mp4
```

### 3. 自定义参数运行
```bash
python main.py --video SAR_Video_First.mp4 \
    --min_displacement 20.0 \
    --min_trajectory_length 5 \
    --max_distance 50.0 \
    --min_area_similarity 0.3 \
    --min_area_threshold 50
```

### 4. 运行测试
```bash
python test_detection.py
```

## 参数说明

### 动目标检测参数
- `min_displacement`: 运动目标最小位移阈值（像素），默认15.0
- `min_trajectory_length`: 运动目标最小轨迹长度（帧数），默认3
- `max_distance`: 阴影区域匹配最大距离（像素），默认40.0
- `min_area_similarity`: 阴影区域匹配最小面积相似度（0-1），默认0.2
- `min_area_threshold`: 阴影区域最小面积阈值（像素²），默认30

### 参数调优建议
- **min_displacement**: 根据目标运动速度调整，值越大检测越严格
- **min_trajectory_length**: 根据视频帧率和目标持续时间调整
- **max_distance**: 根据目标运动速度和帧率调整，值过小会导致轨迹断裂
- **min_area_similarity**: 根据目标形状变化程度调整，值过大会导致匹配失败
- **min_area_threshold**: 根据图像分辨率和目标大小调整，过滤小噪声

## 输出结果

### 1. 可视化图像 (detected/ 文件夹)
- 每帧图像上绘制检测到的运动目标
- 不同颜色表示不同的轨迹
- 显示边界框、质心和轨迹ID

### 2. 检测结果文件 (detection_results.txt)
包含详细的检测信息：
- 运动目标总数
- 每个轨迹的详细信息（起始帧、结束帧、轨迹长度、总位移等）
- 轨迹中每个点的质心坐标和面积

## 性能特点

- **准确性**: 基于物理原理的阴影分析，检测结果可靠
- **鲁棒性**: 支持目标形状和大小的适度变化
- **可配置性**: 丰富的参数设置，适应不同场景需求
- **可视化**: 直观的轨迹可视化，便于结果验证

## 注意事项

1. 确保输入的形态学处理图像质量良好
2. 根据具体应用场景调整检测参数
3. 大面积的静态阴影可能被误判为运动目标，可通过调整面积阈值过滤
4. 系统假设目标阴影在图像中表现为黑色区域（像素值为0）

## 扩展功能

系统设计具有良好的扩展性，可以添加：
- 更复杂的轨迹预测算法
- 基于深度学习的目标分类
- 多目标关联和冲突解决
- 实时处理能力
- 轨迹平滑和插值
