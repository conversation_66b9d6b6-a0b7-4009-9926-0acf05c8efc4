import cv2
import numpy as np
import torch
import torch.nn.functional as F

def adaptive_denoising(image, filter_type='cattepm', 
                       sigma_gaussian=2.0, K_diff=20.0, # CattePM params
                       lambda_val=0.125, iterations=30, # CattePM params
                       lee_kuan_window_size=7, lee_damping_factor=1.0): # Lee/Kuan params
    """
    对图像应用自适应降噪滤波器
    """
    if filter_type == 'cattepm':
        return cattepm_filter(image, sigma_gaussian, K_diff, lambda_val, iterations)
    elif filter_type == 'lee':
        return lee_filter(image, window_size=lee_kuan_window_size, damping_factor=lee_damping_factor)
    elif filter_type == 'kuan':
        return kuan_filter(image, window_size=lee_kuan_window_size)
    # ... (other filters like bilateral, NL-Means could be added here) ...
    # elif filter_type == 'bilateral':
    #     return cv2.bilateralFilter(image, d=9, sigmaColor=75, sigmaSpace=75) # Example params
    # elif filter_type == 'nlmeans':
    #     # Needs grayscale for cv2.fastNlMeansDenoising, or cv2.fastNlMeansDenoisingColored
    #     # Parameters h, templateWindowSize, searchWindowSize need tuning
    #     if len(image.shape) == 3:
    #          return cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
    #     else:
    #          return cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
    else:
        print(f"未知的滤波器类型: {filter_type}，使用CattePM默认参数")
        return cattepm_filter(image, sigma_gaussian, K_diff, lambda_val, iterations)

def cattepm_filter(image, sigma_gaussian=2.0, K_diff=20.0, lambda_val=0.125, iterations=30): # 使用您希望的参数和默认值
    """
    基于CattePM模型的VideoSAR图像降噪
    
    参数:
        image: 输入图像（配准后的图像）
        sigma_gaussian: 高斯核的标准差，用于平滑梯度计算
        K_diff: 扩散系数中的梯度模量参数K (c = exp(-|grad|^2 / K^2))
        lambda_val: 扩散程度的常数
        iterations: 迭代次数
    
    返回:
        降噪后的图像
    """
    # 如果图像是彩色的，转换为灰度图
    is_color = len(image.shape) == 3
    if is_color:
        img_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY).astype(np.float32)
    else:
        img_gray = image.astype(np.float32)
    
    # 创建结果图像 (确保这是在 img_gray 定义之后)
    I = img_gray.copy() # <--- img_gray 在这里被使用
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 高斯核尺寸
    ksize_gaussian = int(3 * sigma_gaussian) // 2 * 2 + 1  # 确保是奇数
    
    # 预先创建卷积核 (与您之前的代码或我的建议一致)
    sobel_x = torch.tensor([[[[1, 0, -1], [2, 0, -2], [1, 0, -1]]]], dtype=torch.float32, device=device)
    sobel_y = torch.tensor([[[[1, 2, 1], [0, 0, 0], [-1, -2, -1]]]], dtype=torch.float32, device=device)
    laplacian_kernel_pm = torch.tensor([[[[0, 1, 0], [1, -4, 1], [0, 1, 0]]]], dtype=torch.float32, device=device) # 4-connectivity laplacian

    # 迭代应用CattePM
    for _ in range(iterations): # 使用 _ 如果循环变量未使用，或者用 iter_num
        # 步骤1: 应用高斯滤波 G_σ * I
        I_smoothed = cv2.GaussianBlur(I, (ksize_gaussian, ksize_gaussian), sigma_gaussian) # 使用 I
        
        # 将NumPy数组转换为PyTorch张量
        I_tensor = torch.from_numpy(I.astype(np.float32)).to(device).unsqueeze(0).unsqueeze(0)
        I_smoothed_tensor = torch.from_numpy(I_smoothed.astype(np.float32)).to(device).unsqueeze(0).unsqueeze(0)
        
        # 步骤2: 计算平滑后图像的梯度 ∇(G_σ * I)
        gx_smoothed = F.conv2d(I_smoothed_tensor, sobel_x, padding=1)
        gy_smoothed = F.conv2d(I_smoothed_tensor, sobel_y, padding=1)
        
        # 计算梯度模值 |∇(G_σ * I)|
        grad_mag_smoothed = torch.sqrt(gx_smoothed**2 + gy_smoothed**2 + 1e-10)
        
        # 步骤3: 计算扩散系数 c(|∇G_σ * I|)
        # 注意 K_diff 是 K, 所以分母是 K_diff^2
        c = torch.exp(-(grad_mag_smoothed**2) / (K_diff**2 + 1e-10))
        
        # 步骤4: 更新图像
        # 使用简化的更新: I_t = lambda * c * Laplacian(I)
        delta_I_laplacian = F.conv2d(I_tensor, laplacian_kernel_pm, padding=1)
        I_tensor = I_tensor + lambda_val * (c * delta_I_laplacian)
        
        # 更新I为下一次迭代准备
        I = I_tensor.squeeze().cpu().numpy()
    
    # 确保值在有效范围内
    result = np.clip(I, 0, 255).astype(np.uint8)
    
    # 如果原始图像是彩色的，将处理结果应用到每个通道
    if is_color:
        # 如果原始就是彩色，而我们只处理了灰度，需要将结果放回彩色图像
        # 或者，如果目标是对每个通道分别进行CattePM，则整个逻辑需要调整
        # 当前实现是将灰度结果复制到所有通道
        output_img = cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)
        return output_img
    else:
        return result

def lee_filter(image, window_size=7, damping_factor=1.0):
    """
    李氏滤波器实现
    
    参数:
        image: 输入图像
        window_size: 滑动窗口大小
        damping_factor: 阻尼系数
    
    返回:
        降噪后的图像
    """
    # 这里为了保持兼容性实现简易版李氏滤波器
    img = image.copy()
    if len(img.shape) == 3:
        img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    img_mean = cv2.blur(img, (window_size, window_size))
    img_sqr_mean = cv2.blur(img**2, (window_size, window_size))
    img_variance = img_sqr_mean - img_mean**2
    
    overall_variance = np.mean(img_variance)
    
    img_weights = img_variance / (img_variance + overall_variance * damping_factor)
    output = img_mean + img_weights * (img - img_mean)
    
    output = np.clip(output, 0, 255).astype(np.uint8)
    
    if len(image.shape) == 3:
        rgb_output = np.zeros_like(image)
        for i in range(3):
            rgb_output[:, :, i] = output
        return rgb_output
    else:
        return output

def kuan_filter(image, window_size=7):
    """
    宽氏滤波器实现
    
    参数:
        image: 输入图像
        window_size: 滑动窗口大小
    
    返回:
        降噪后的图像
    """
    # 这里为了保持兼容性实现简易版宽氏滤波器
    img = image.copy()
    if len(img.shape) == 3:
        img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 计算局部统计量
    mean = cv2.blur(img, (window_size, window_size))
    mean_sqr = mean**2
    var = cv2.blur(img**2, (window_size, window_size)) - mean_sqr
    
    # 估计噪声方差
    noise_var = np.mean(var)
    
    # 计算滤波系数
    cu = np.sqrt(1 + noise_var / (mean_sqr + 1e-10))
    
    # 应用Kuan滤波
    k = (1 - (1/cu)) / (1 + (1/cu))
    output = mean + k * (img - mean)
    
    output = np.clip(output, 0, 255).astype(np.uint8)
    
    if len(image.shape) == 3:
        rgb_output = np.zeros_like(image)
        for i in range(3):
            rgb_output[:, :, i] = output
        return rgb_output
    else:
        return output 