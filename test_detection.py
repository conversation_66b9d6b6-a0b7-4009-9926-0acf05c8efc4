#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动目标检测功能的脚本
"""

import os
import sys
from detection import detect_motion_targets

def test_motion_detection():
    """测试动目标检测功能"""
    print("=" * 60)
    print("SAR视频动目标检测测试")
    print("=" * 60)
    
    # 检查必要的文件夹是否存在
    required_folders = ["post_processed", "registered"]
    for folder in required_folders:
        if not os.path.exists(folder):
            print(f"错误：缺少必要的文件夹 {folder}")
            return False
    
    # 检查post_processed文件夹中的图像数量
    post_files = [f for f in os.listdir("post_processed") if f.endswith(".png")]
    reg_files = [f for f in os.listdir("registered") if f.endswith(".png")]
    
    print(f"形态学处理后图像数量: {len(post_files)}")
    print(f"配准后图像数量: {len(reg_files)}")
    
    if len(post_files) == 0:
        print("错误：post_processed文件夹中没有图像文件")
        return False
    
    # 执行动目标检测
    print("\n开始动目标检测...")
    try:
        motion_trajectories = detect_motion_targets(
            post_processed_folder="post_processed",
            original_frames_folder="registered",
            output_folder="detected",
            min_displacement=15.0,  # 最小位移15像素
            min_trajectory_length=3,  # 最少3帧
            max_distance=40.0,  # 最大匹配距离40像素
            min_area_similarity=0.2,  # 最小面积相似度0.2
            min_area_threshold=30  # 最小面积30像素²
        )
        
        print(f"\n检测完成！")
        print(f"检测到的运动目标数量: {len(motion_trajectories)}")
        
        # 显示前5个轨迹的详细信息
        print("\n前5个运动目标轨迹详情:")
        for i, traj in enumerate(motion_trajectories[:5]):
            print(f"  轨迹 {i+1} (ID: {traj.trajectory_id}):")
            print(f"    起始帧: {traj.start_frame}, 结束帧: {traj.end_frame}")
            print(f"    轨迹长度: {traj.get_trajectory_length()} 帧")
            print(f"    总位移: {traj.get_total_displacement():.2f} 像素")
            print(f"    平均面积: {sum(r.area for r in traj.regions) / len(traj.regions):.2f} 像素²")
        
        # 检查输出文件
        if os.path.exists("detected"):
            detected_files = [f for f in os.listdir("detected") if f.endswith(".png")]
            print(f"\n可视化结果文件数量: {len(detected_files)}")
        
        if os.path.exists("detection_results.txt"):
            print("检测结果文件已生成: detection_results.txt")
        
        print("\n测试成功完成！")
        return True
        
    except Exception as e:
        print(f"检测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_motion_detection()
    
    if success:
        print("\n" + "=" * 60)
        print("动目标检测功能测试通过！")
        print("您可以查看以下文件:")
        print("- detected/ 文件夹: 可视化结果图像")
        print("- detection_results.txt: 详细检测结果")
        print("=" * 60)
        sys.exit(0)
    else:
        print("\n" + "=" * 60)
        print("动目标检测功能测试失败！")
        print("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    main()
