import cv2
import numpy as np

def register_frames(prev_frame, current_frame):
    """
    使用SIFT和RANSAC进行图像配准

    参数:
        prev_frame: 前一帧图像
        current_frame: 当前帧图像

    返回:
        registered_frame: 配准后的当前帧
        homography: 计算得到的单应性矩阵
    """
    # 转换为灰度图
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY) if len(prev_frame.shape) == 3 else prev_frame
    curr_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame

    # 创建SIFT对象 - 兼容不同版本的OpenCV
    try:
        # OpenCV 4.x
        sift = cv2.SIFT_create()
    except AttributeError:
        try:
            # OpenCV 3.x
            sift = cv2.xfeatures2d.SIFT_create()
        except AttributeError:
            # 如果上述方法都失败，尝试使用ORB作为替代
            print("SIFT不可用，使用ORB作为替代")
            sift = cv2.ORB_create()

    # 检测关键点和描述符
    kp1, des1 = sift.detectAndCompute(prev_gray, None)
    kp2, des2 = sift.detectAndCompute(curr_gray, None)

    # 如果没有足够的特征点，返回原始帧
    if des1 is None or des2 is None or len(kp1) < 4 or len(kp2) < 4:
        return current_frame, np.eye(3)

    # 使用FLANN匹配器进行特征匹配
    FLANN_INDEX_KDTREE = 1
    index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
    search_params = dict(checks=50)
    flann = cv2.FlannBasedMatcher(index_params, search_params)

    matches = flann.knnMatch(des1, des2, k=2)

    # 应用比率测试筛选良好匹配
    good_matches = []
    for m, n in matches:
        if m.distance < 0.7 * n.distance:
            good_matches.append(m)

    # 如果没有足够的良好匹配，返回原始帧
    if len(good_matches) < 4:
        return current_frame, np.eye(3)

    # 提取匹配点的坐标
    src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
    dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)

    # 使用RANSAC计算单应性矩阵
    homography, mask = cv2.findHomography(dst_pts, src_pts, cv2.RANSAC, 5.0)

    if homography is None:
        return current_frame, np.eye(3)

    # 应用变换
    h, w = prev_gray.shape
    registered_frame = cv2.warpPerspective(current_frame, homography, (w, h))

    return registered_frame, homography

def visualize_registration(prev_frame, current_frame, registered_frame):
    """
    可视化配准结果

    参数:
        prev_frame: 前一帧
        current_frame: 当前帧
        registered_frame: 配准后的当前帧

    返回:
        visualization: 可视化结果
    """
    # 转换为灰度图
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY) if len(prev_frame.shape) == 3 else prev_frame
    curr_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY) if len(current_frame.shape) == 3 else current_frame
    reg_gray = cv2.cvtColor(registered_frame, cv2.COLOR_BGR2GRAY) if len(registered_frame.shape) == 3 else registered_frame

    # 创建差异图像
    diff_before = cv2.absdiff(prev_gray, curr_gray)
    diff_after = cv2.absdiff(prev_gray, reg_gray)

    # 创建可视化图像
    h, w = prev_gray.shape
    visualization = np.zeros((h, w * 3), dtype=np.uint8)

    # 将三个图像并排放置
    visualization[:, 0:w] = prev_gray
    visualization[:, w:2*w] = curr_gray
    visualization[:, 2*w:3*w] = reg_gray

    return visualization
