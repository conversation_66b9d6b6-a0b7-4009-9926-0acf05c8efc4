#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强动目标检测功能的脚本 - 结合帧间差分和阴影轨迹分析
"""

import os
import sys
from detection import detect_motion_targets

def test_enhanced_motion_detection():
    """测试增强动目标检测功能"""
    print("=" * 70)
    print("SAR视频增强动目标检测测试 - 结合帧间差分和阴影轨迹分析")
    print("=" * 70)
    
    # 检查必要的文件夹是否存在
    required_folders = ["post_processed", "registered"]
    for folder in required_folders:
        if not os.path.exists(folder):
            print(f"错误：缺少必要的文件夹 {folder}")
            return False
    
    # 检查文件夹中的图像数量
    post_files = [f for f in os.listdir("post_processed") if f.endswith(".png")]
    reg_files = [f for f in os.listdir("registered") if f.endswith(".png")]
    
    print(f"形态学处理后图像数量: {len(post_files)}")
    print(f"配准后图像数量: {len(reg_files)}")
    
    if len(post_files) == 0:
        print("错误：post_processed文件夹中没有图像文件")
        return False
    
    if len(reg_files) == 0:
        print("警告：registered文件夹中没有图像文件，将禁用帧间差分")
    
    # 测试1: 仅使用阴影轨迹分析
    print("\n" + "="*50)
    print("测试1: 仅使用阴影轨迹分析")
    print("="*50)
    
    try:
        motion_trajectories_shadow = detect_motion_targets(
            post_processed_folder="post_processed",
            original_frames_folder="registered",
            output_folder="detected_shadow_only",
            min_displacement=15.0,
            min_trajectory_length=3,
            max_distance=40.0,
            min_area_similarity=0.2,
            min_area_threshold=30,
            use_frame_diff=False,  # 禁用帧间差分
            diff_threshold=30.0,
            diff_weight=0.3
        )
        
        print(f"仅阴影轨迹分析检测到的运动目标数量: {len(motion_trajectories_shadow)}")
        
    except Exception as e:
        print(f"阴影轨迹分析测试失败: {e}")
        return False
    
    # 测试2: 结合帧间差分和阴影轨迹分析
    print("\n" + "="*50)
    print("测试2: 结合帧间差分和阴影轨迹分析")
    print("="*50)
    
    try:
        motion_trajectories_enhanced = detect_motion_targets(
            post_processed_folder="post_processed",
            original_frames_folder="registered",
            output_folder="detected_enhanced",
            min_displacement=15.0,
            min_trajectory_length=3,
            max_distance=40.0,
            min_area_similarity=0.2,
            min_area_threshold=30,
            use_frame_diff=True,  # 启用帧间差分
            diff_threshold=30.0,
            diff_weight=0.3
        )
        
        print(f"增强检测到的运动目标数量: {len(motion_trajectories_enhanced)}")
        
        # 比较结果
        print(f"\n检测结果比较:")
        print(f"仅阴影轨迹分析: {len(motion_trajectories_shadow)} 个目标")
        print(f"增强检测方法: {len(motion_trajectories_enhanced)} 个目标")
        
        improvement = len(motion_trajectories_enhanced) - len(motion_trajectories_shadow)
        if improvement > 0:
            print(f"增强检测多发现了 {improvement} 个目标 (+{improvement/len(motion_trajectories_shadow)*100:.1f}%)")
        elif improvement < 0:
            print(f"增强检测减少了 {-improvement} 个误检目标")
        else:
            print("两种方法检测到相同数量的目标")
        
        # 显示前5个轨迹的详细信息
        print(f"\n增强检测前5个运动目标轨迹详情:")
        for i, traj in enumerate(motion_trajectories_enhanced[:5]):
            print(f"  轨迹 {i+1} (ID: {traj.trajectory_id}):")
            print(f"    起始帧: {traj.start_frame}, 结束帧: {traj.end_frame}")
            print(f"    轨迹长度: {traj.get_trajectory_length()} 帧")
            print(f"    总位移: {traj.get_total_displacement():.2f} 像素")
            print(f"    平均面积: {sum(r.area for r in traj.regions) / len(traj.regions):.2f} 像素²")
        
        # 检查输出文件
        output_folders = ["detected_shadow_only", "detected_enhanced"]
        for folder in output_folders:
            if os.path.exists(folder):
                detected_files = [f for f in os.listdir(folder) if f.endswith(".png")]
                print(f"\n{folder} 可视化结果文件数量: {len(detected_files)}")
        
        print("\n测试成功完成！")
        return True
        
    except Exception as e:
        print(f"增强检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_parameters():
    """测试不同参数组合的效果"""
    print("\n" + "="*50)
    print("测试不同参数组合")
    print("="*50)
    
    parameter_sets = [
        {
            "name": "高敏感度",
            "diff_threshold": 20.0,
            "diff_weight": 0.4,
            "min_displacement": 10.0
        },
        {
            "name": "中等敏感度",
            "diff_threshold": 30.0,
            "diff_weight": 0.3,
            "min_displacement": 15.0
        },
        {
            "name": "低敏感度",
            "diff_threshold": 40.0,
            "diff_weight": 0.2,
            "min_displacement": 20.0
        }
    ]
    
    for params in parameter_sets:
        print(f"\n测试 {params['name']} 参数:")
        print(f"  差分阈值: {params['diff_threshold']}")
        print(f"  差分权重: {params['diff_weight']}")
        print(f"  最小位移: {params['min_displacement']}")
        
        try:
            motion_trajectories = detect_motion_targets(
                post_processed_folder="post_processed",
                original_frames_folder="registered",
                output_folder=f"detected_{params['name'].lower()}",
                min_displacement=params['min_displacement'],
                min_trajectory_length=3,
                max_distance=40.0,
                min_area_similarity=0.2,
                min_area_threshold=30,
                use_frame_diff=True,
                diff_threshold=params['diff_threshold'],
                diff_weight=params['diff_weight']
            )
            
            print(f"  检测到的目标数量: {len(motion_trajectories)}")
            
        except Exception as e:
            print(f"  参数测试失败: {e}")

def main():
    """主函数"""
    success = test_enhanced_motion_detection()
    
    if success:
        # 测试不同参数
        test_different_parameters()
        
        print("\n" + "=" * 70)
        print("增强动目标检测功能测试通过！")
        print("主要改进:")
        print("- 结合帧间差分和阴影轨迹分析")
        print("- 降低漏检概率")
        print("- 提供多种参数配置选项")
        print("\n您可以查看以下文件:")
        print("- detected_shadow_only/ : 仅阴影轨迹分析结果")
        print("- detected_enhanced/ : 增强检测结果")
        print("- detection_results.txt : 详细检测结果")
        print("=" * 70)
        sys.exit(0)
    else:
        print("\n" + "=" * 70)
        print("增强动目标检测功能测试失败！")
        print("=" * 70)
        sys.exit(1)

if __name__ == "__main__":
    main()
